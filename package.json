{"name": "happy-shop-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"start": "nest start", "dev": "nest start --watch", "debug": "nest start --debug --watch", "build": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write src/", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/jwt": "^11.0.0", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/swagger": "^11.2.0", "@nestjs/typeorm": "11.0.0", "@nestjs/websockets": "^11.1.3", "@types/multer": "^1.4.13", "axios": "^1.10.0", "bcrypt": "^6.0.0", "cache-manager": "^7.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "class-validator-jsonschema": "^5.0.2", "dayjs": "^1.11.13", "extensionsjs": "^1.2.0", "fast-glob": "^3.3.3", "hbs": "^4.2.0", "mssql": "^11.0.1", "nanoid": "5.1.5", "nestjs-i18n": "^10.5.1", "nestjs-typeorm3-kit": "^0.1.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "pg": "^8.16.2", "qrcode": "^1.5.4", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.2", "typeorm": "0.3.25", "typeorm-transactional": "^0.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/bcrypt": "^5.0.2", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/node": "^24.0.4", "@types/qrcode": "^1.5.5", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.29.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "jest": "^30.0.3", "prettier": "^3.6.2", "source-map-support": "^0.5.21", "supertest": "^7.1.1", "ts-jest": "^29.4.0", "ts-loader": "^9.5.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}