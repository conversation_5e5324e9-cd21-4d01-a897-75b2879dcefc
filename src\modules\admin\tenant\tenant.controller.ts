import { DefController, DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { AdminTenantService } from './tenant.service';
import { Body, Req } from '@nestjs/common';
import { CreateTenantAppReq, RegisterTenantReq } from './dto';
import { Request } from 'express';

@DefController('tenants')
export class AdminTenantController {
    constructor(private readonly adminTenantService: AdminTenantService) {}

    @DefPost('create-application', {
        summary: 'Create application',
    })
    createApplication() {
        return this.adminTenantService.createApplication();
    }

    @DefPost('register', {
        summary: 'Create tenant and make root account',
    })
    register(@Body() body: RegisterTenantReq) {
        return this.adminTenantService.register(body);
    }

    @DefGet('list-applications', {
        summary: 'List applications',
    })
    listApplications(@Req() req: Request) {
        const domain = req.protocol + '://' + req.get('host');
        return this.adminTenantService.listApplications(domain);
    }
}
