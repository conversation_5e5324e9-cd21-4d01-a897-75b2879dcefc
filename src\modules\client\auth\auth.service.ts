import { Injectable, UnauthorizedException } from '@nestjs/common';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';
import * as jwt from 'jsonwebtoken';
import { InjectRepo } from 'nestjs-typeorm3-kit';
import {
    AccountRepo,
    AuthorizationCodeRepo,
    TokenRepo,
    TenantApplicationRepo, // sửa chỗ này
} from '~/domains/primary';
import { NSAccount } from '~/common/enums';
import { JwtService } from '@nestjs/jwt';
@Injectable()
export class AuthService {
    constructor(
        private readonly jwtService: JwtService,
        @InjectRepo(AccountRepo)
        private readonly accountRepo: AccountRepo,
        @InjectRepo(TenantApplicationRepo)
        private readonly tenantAppRepo: TenantApplicationRepo, // sửa chỗ này
        @InjectRepo(AuthorizationCodeRepo)
        private readonly codeRepo: AuthorizationCodeRepo,
        @InjectRepo(TokenRepo)
        private readonly tokenRepo: TokenRepo,
    ) {}

    async validateLoginAndGenerateCode(
        username: string,
        password: string,
        clientId: string,
        redirectUri: string,
    ): Promise<string | null> {
        // 1. Lấy thông tin mapping tenant-application
        const tenantApp = await this.tenantAppRepo.findOne({ where: { clientId, isActive: true } });
        if (!tenantApp || !tenantApp.redirectUris.includes(redirectUri)) return null;

        // 2. Tìm account thuộc đúng tenant này (multi-tenant)
        const account = await this.accountRepo.findOne({
            where: {
                username,
                tenantId: tenantApp.tenantId, // chỉ tìm user trong tenant đúng!
                status: NSAccount.EStatus.ACTIVE,
            },
        });
        if (!account) return null;
        const ok = await bcrypt.compare(password, account.password);
        if (!ok) return null;

        // 3. Sinh code
        const code = crypto.randomBytes(16).toString('hex');
        await this.codeRepo.save({
            accountId: account.id,
            clientId,
            code,
            expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),
            redirectUri,
            scope: ['openid', 'profile'],
        });
        return code;
    }

    async exchangeCodeForToken(
        code: string,
        clientId: string,
        clientSecret: string,
        redirectUri: string,
    ) {
        // 1. Lấy mapping tenant-application đúng
        const tenantApp = await this.tenantAppRepo.findOne({
            where: { clientId, clientSecret, isActive: true },
        });
        if (!tenantApp || !tenantApp.redirectUris.includes(redirectUri)) {
            throw new UnauthorizedException('Invalid client credentials or redirectUri');
        }

        // 2. Kiểm tra code
        const codeEntity = await this.codeRepo.findOne({ where: { code, clientId, redirectUri } });
        console.log(codeEntity);
        if (!codeEntity) {
            throw new UnauthorizedException('Invalid or expired code');
        }

        // 3. Sinh token (JWT)
        const payload = { sub: codeEntity.accountId, clientId };
        const accessToken = await this.jwtService.signAsync(payload);
        const refreshToken = await this.jwtService.signAsync(payload);

        const account = await this.accountRepo.findOne({ where: { id: codeEntity.accountId } });
        if (!account) {
            throw new UnauthorizedException('Invalid or expired code');
        }

        await this.tokenRepo.save({
            accountId: codeEntity.accountId,
            clientId,
            accessToken,
            refreshToken,
            expiresAt: new Date(Date.now() + 3600 * 1000),
            scope: ['openid', 'profile'],
        });
        const result = {
            access_token: accessToken,
            refresh_token: refreshToken,
            expires_in: 3600,
            token_type: 'Bearer',
        };
        console.log(`==========`);
        console.log(result);
        console.log(`==========`);
        return result;
    }

    async getUserInfo({ id }: { id: string }) {
        const account = await this.accountRepo.findOne({ where: { id } });
        return {
            id: account.id,
            username: account.username,
            fullName: account?.fullName,
            avatar: account?.avatar,
        };
    }
}
