import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Entity, Column } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('application')
export class ApplicationEntity extends PrimaryBaseEntity {
    @ApiProperty()
    @Column({ unique: true })
    code: string; // VD: "CRM", "APE_CHAIN"

    @ApiProperty()
    @Column({ length: 255 })
    name: string; // Tên hiển thị app

    @ApiPropertyOptional()
    @Column({ type: 'text', nullable: true })
    description: string;
}
