PORT=4501
TZ=UTC
REQUEST_TIMEOUT=180000
#Swagger Config
SWAGGER_TITLE="APE CRM API"
SWAGGER_DESCRIPTION="The APE CRM API"
SWAGGER_VERSION="1.0"
# Primary Database
DB_PRIMARY_TYPE="postgres"
DB_PRIMARY_HOST="localhost"
DB_PRIMARY_PORT=5432
DB_PRIMARY_USERNAME="postgres"
DB_PRIMARY_PASSWORD="Abc12345"
DB_PRIMARY_DATABASE="ape-crm-dev"
DB_PRIMARY_SYNCHRONIZE=true
DB_PRIMARY_SSL=false
DB_PRIMARY_SSL_REJECT_UNAUTHORIZED=true
# JWT HS256 config
JWT_SECRET="/q5zjNG6W0cbEdseJEySMY7xrN/5BVCK5j/CaILyRvo="
JWT_EXPIRY="100d"
JWT_REFRESH_TOKEN_SECRET="/A5zjN26W0cbEdseJEDsMY7xrN/5BVCK5j/ZolUyYbi="
JWT_REFRESH_TOKEN_EXPIRY="300d"

