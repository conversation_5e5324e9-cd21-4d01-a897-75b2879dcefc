/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "enum": {
        "NSMember": {
            "EAddressType": {
                "HOME": string;
                "COMPANY": string;
            };
            "EMembershipType": {
                "PERSONAL": string;
                "ENTERPRISE": string;
            };
            "EMemberType": {
                "MEMBER": string;
                "COLLABORATOR": string;
                "POST_OFFICE": string;
            };
            "EStatus": {
                "INACTIVE": string;
                "ACTIVE": string;
                "WAITING_FOR_VERIFY": string;
                "DELETED": string;
                "WAITING_FOR_APPROVE": string;
            };
            "EBusinessType": {
                "LED_ADVERTISEMENT": string;
                "SOCIAL_SECURITY_CARD": string;
                "TOURISM": string;
                "HEALTH": string;
                "SME360": string;
            };
        };
    };
    "member_auth": {
        "login": {
            "error": {
                "member_not_existed": string;
                "wrong_password": string;
            };
        };
        "register": {
            "error": {
                "member_existed": string;
            };
        };
    };
    "validation": {
        "NOT_EMPTY": string;
        "INVALID_EMAIL": string;
        "INVALID_BOOLEAN": string;
        "MIN": string;
        "MAX": string;
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
