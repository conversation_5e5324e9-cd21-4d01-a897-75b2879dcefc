import { Entity, PrimaryColumn, Column, Index } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('tenant_application')
@Index(['tenantId', 'applicationId'], { unique: true })
export class TenantApplicationEntity extends PrimaryBaseEntity {
    @PrimaryColumn({ type: 'uuid' })
    tenantId: string;

    @PrimaryColumn({ type: 'uuid' })
    applicationId: string;

    @Column({ unique: true })
    clientId: string; // Duy nhất cho mỗi tenant-app

    @Column()
    clientSecret: string;

    @Column({ type: 'text', array: true })
    redirectUris: string[];

    @Column({ default: true })
    isActive: boolean;

    @Column({ length: 50, default: 'default' })
    plan: string;
}
