import {
    Controller,
    Get,
    Post,
    Req,
    Res,
    Body,
    Render,
    Query,
    Headers,
    UseGuards,
} from '@nestjs/common';
import { AuthService } from './auth.service';
import { Response, Request } from 'express';
import { DefGet, DefPost } from 'nestjs-typeorm3-kit';
import { JwtService } from '@nestjs/jwt';
import { JwtAuthGuard } from '../@providers/jwt-auth.guard';

@Controller('auth')
export class AuthController {
    constructor(
        private readonly authService: AuthService,
        private readonly jwtService: JwtService,
    ) {}

    @Get('authorize')
    @Render('login')
    renderLogin(@Query() query: any) {
        return {
            redirectUri: query.redirect_uri,
            clientId: query.client_id,
            state: query.state,
            error: query.error,
        };
    }

    @Post('authorize')
    async doLogin(@Body() body: any, @Res() res: Response) {
        const { username, password, client_id: clientId, redirect_uri: redirectUri, state } = body;
        const code = await this.authService.validateLoginAndGenerateCode(
            username,
            password,
            clientId,
            redirectUri,
        );

        if (!code) {
            return res.render('login', {
                redirectUri,
                clientId,
                state,
                error: 'Sai tên đăng nhập hoặc mật khẩu!',
            });
        }

        const url = new URL(redirectUri);
        url.searchParams.set('code', code);
        if (state) url.searchParams.set('state', state);
        return res.redirect(url.toString());
    }

    @DefPost('token')
    async token(@Body() body: any) {
        console.log(`=====TOKEN ENDPOINT=====`);
        const {
            code,
            client_id: clientId,
            client_secret: clientSecret,
            redirect_uri: redirectUri,
        } = body;
        return this.authService.exchangeCodeForToken(code, clientId, clientSecret, redirectUri);
    }
    @DefGet('userinfo')
    @UseGuards(JwtAuthGuard)
    userInfo(@Req() req) {
        const user = req.user;
        return this.authService.getUserInfo({
            id: user.id,
        });
    }
}
