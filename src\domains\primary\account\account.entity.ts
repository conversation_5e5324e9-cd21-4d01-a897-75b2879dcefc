import { Entity, Column, Index } from 'typeorm';
import { NSAccount } from '~/common/enums';
import { PrimaryBaseEntity } from '~/domains/primary/primary-base.entity';

@Entity('account')
@Index(['tenantId', 'username'], { unique: true })
export class AccountEntity extends PrimaryBaseEntity {
  @Column({ type: 'uuid' })
  tenantId: string;

  @Column({ length: 255 })
  username: string;

  @Column({ length: 255 })
  password: string;

  @Column({ length: 255, nullable: true })
  fullName?: string;

  @Column({ length: 1024, nullable: true })
  avatar?: string;

  @Column({ default: false })
  isRoot: boolean;

  @Column({ default: NSAccount.EStatus.INACTIVE })
  status: NSAccount.EStatus;
}
